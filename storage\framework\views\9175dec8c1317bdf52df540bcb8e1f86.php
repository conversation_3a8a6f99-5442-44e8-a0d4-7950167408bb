<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['comment', 'post']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['comment', 'post']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="comment-item" data-comment-id="<?php echo e($comment->id); ?>">
    <div class="flex space-x-3">
        <a href="<?php echo e(route('profile.user', $comment->user)); ?>">
            <img class="h-10 w-10 rounded-full flex-shrink-0" 
                 src="<?php echo e($comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                 alt="<?php echo e($comment->user->name); ?>">
        </a>
        <div class="flex-1">
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-1">
                    <a href="<?php echo e(route('profile.user', $comment->user)); ?>" class="font-medium text-gray-900 hover:text-custom-green">
                        <?php echo e($comment->user->name); ?>

                    </a>
                    <span class="text-sm text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                </div>
                <div class="comment-content">
                    <p class="text-gray-700"><?php echo nl2br(e($comment->content)); ?></p>
                </div>
                
                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-2">
                    <form class="edit-comment-form" data-comment-id="<?php echo e($comment->id); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <textarea name="content" rows="2" 
                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none"><?php echo e($comment->content); ?></textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment(<?php echo e($comment->id); ?>)" 
                                    class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                            <button type="submit" 
                                    class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 text-sm">
                <?php if(auth()->guard()->check()): ?>
                    <button onclick="toggleCommentLike(<?php echo e($comment->id); ?>)"
                            class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                            id="comment-like-btn-<?php echo e($comment->id); ?>">
                        <svg class="w-4 h-4 <?php echo e($comment->isLikedBy(auth()->user()) ? 'text-red-600 fill-current' : ''); ?>"
                             fill="<?php echo e($comment->isLikedBy(auth()->user()) ? 'currentColor' : 'none'); ?>"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span id="comment-like-count-<?php echo e($comment->id); ?>"><?php echo e($comment->likes->count()); ?></span>
                    </button>
                <?php else: ?>
                    <span class="flex items-center space-x-1 text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span><?php echo e($comment->likes->count()); ?></span>
                    </span>
                <?php endif; ?>
                
                <?php if(auth()->guard()->check()): ?>
                    <button onclick="showReplyForm(<?php echo e($comment->id); ?>)" class="text-gray-500 hover:text-blue-600 transition-colors">
                        Reply
                    </button>
                <?php endif; ?>

                <?php if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin())): ?>
                    <button onclick="editComment(<?php echo e($comment->id); ?>)" class="text-gray-500 hover:text-blue-600 transition-colors">
                        Edit
                    </button>
                    <button onclick="deleteComment(<?php echo e($comment->id); ?>)" class="text-gray-500 hover:text-red-600 transition-colors">
                        Delete
                    </button>
                <?php endif; ?>
            </div>
            
            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3" id="reply-form-<?php echo e($comment->id); ?>">
                <?php if(auth()->guard()->check()): ?>
                    <form class="comment-form" data-post-id="<?php echo e($post->id); ?>" data-parent-id="<?php echo e($comment->id); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="flex space-x-3">
                            <img class="h-8 w-8 rounded-full flex-shrink-0" 
                                 src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                 alt="<?php echo e(auth()->user()->name); ?>">
                            <div class="flex-1">
                                <textarea name="content" rows="2" 
                                          placeholder="Write a reply..." 
                                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" 
                                          required></textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm(<?php echo e($comment->id); ?>)" 
                                            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button type="submit" 
                                            class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
            
            <!-- Replies -->
            <?php if($comment->replies->count() > 0): ?>
                <div class="nested-comments mt-4 space-y-3">
                    <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if (isset($component)) { $__componentOriginald908f04fb1ba83f78e519c9f401232b9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald908f04fb1ba83f78e519c9f401232b9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.comment-item','data' => ['comment' => $reply,'post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($reply),'post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $attributes = $__attributesOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__attributesOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald908f04fb1ba83f78e519c9f401232b9)): ?>
<?php $component = $__componentOriginald908f04fb1ba83f78e519c9f401232b9; ?>
<?php unset($__componentOriginald908f04fb1ba83f78e519c9f401232b9); ?>
<?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/comment-item.blade.php ENDPATH**/ ?>