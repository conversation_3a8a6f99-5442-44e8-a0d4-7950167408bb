<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['post', 'showInline' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['post', 'showInline' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="comment-section" data-post-id="<?php echo e($post->id); ?>">
    <?php if(!$showInline): ?>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    Comments (<span id="comments-count-<?php echo e($post->id); ?>"><?php echo e($post->comments->count()); ?></span>)
                </h3>
    <?php endif; ?>
                
    <!-- Add Comment Form -->
    <?php if(auth()->guard()->check()): ?>
        <form class="comment-form mb-6" data-post-id="<?php echo e($post->id); ?>" data-parent-id="">
            <?php echo csrf_field(); ?>
            <div class="flex space-x-3">
                <img class="h-10 w-10 rounded-full flex-shrink-0" 
                     src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                     alt="<?php echo e(auth()->user()->name); ?>">
                <div class="flex-1">
                    <textarea name="content" rows="3" 
                              placeholder="Write a comment..." 
                              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" 
                              required></textarea>
                    <div class="mt-2 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            Press Ctrl+Enter to post
                        </div>
                        <div class="flex space-x-2">
                            <button type="button" class="cancel-comment-btn hidden px-3 py-1 text-sm text-gray-600 hover:text-gray-800">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                                Post Comment
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    <?php else: ?>
        <div class="mb-6 p-4 bg-gray-50 rounded-lg text-center">
            <p class="text-gray-600">
                <a href="<?php echo e(route('login')); ?>" class="text-custom-green hover:text-custom-second-darkest font-medium">Sign in</a> 
                to join the conversation
            </p>
        </div>
    <?php endif; ?>

    <!-- Comments List -->
    <div class="comments-list space-y-4" id="comments-list-<?php echo e($post->id); ?>">
        <?php $__empty_1 = true; $__currentLoopData = $post->comments->whereNull('parent_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php echo $__env->make('components.comment-item', ['comment' => $comment, 'post' => $post], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="no-comments text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p>No comments yet. Be the first to comment!</p>
            </div>
        <?php endif; ?>
    </div>

    <?php if(!$showInline): ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.comment-section .comment-form textarea:focus {
    min-height: 80px;
}

.comment-section .reply-form {
    margin-top: 12px;
    padding-left: 40px;
}

.comment-section .comment-item {
    transition: background-color 0.2s ease;
}

.comment-section .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

.comment-section .comment-actions button {
    transition: all 0.2s ease;
}

.comment-section .comment-actions button:hover {
    transform: translateY(-1px);
}

.comment-section .nested-comments {
    border-left: 2px solid #e5e7eb;
    margin-left: 20px;
    padding-left: 20px;
}
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/comment-section.blade.php ENDPATH**/ ?>