<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['share', 'showInline' => false]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['share', 'showInline' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="p-4 bg-gray-50">
    <!-- Comment Form -->
    <?php if(auth()->guard()->check()): ?>
        <form class="share-comment-form mb-4" data-share-id="<?php echo e($share->id); ?>">
            <?php echo csrf_field(); ?>
            <div class="flex space-x-3">
                <img class="h-8 w-8 rounded-full" 
                     src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                     alt="<?php echo e(auth()->user()->name); ?>">
                <div class="flex-1">
                    <textarea name="content" rows="2" 
                              placeholder="Write a comment..." 
                              class="w-full border-gray-300 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                    <div class="mt-2 flex justify-end">
                        <button type="submit" 
                                class="px-4 py-1 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                            Comment
                        </button>
                    </div>
                </div>
            </div>
        </form>
    <?php endif; ?>

    <!-- Comments List -->
    <div class="space-y-3" id="share-comments-list-<?php echo e($share->id); ?>">
        <?php $__empty_1 = true; $__currentLoopData = $share->comments()->whereNull('parent_id')->with(['user', 'replies.user', 'likes'])->latest()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="comment-item" data-comment-id="<?php echo e($comment->id); ?>">
                <div class="flex space-x-3">
                    <img class="h-8 w-8 rounded-full" 
                         src="<?php echo e($comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                         alt="<?php echo e($comment->user->name); ?>">
                    <div class="flex-1">
                        <div class="bg-white rounded-lg px-3 py-2 shadow-sm">
                            <div class="flex items-center justify-between mb-1">
                                <span class="font-medium text-gray-900 text-sm"><?php echo e($comment->user->name); ?></span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                    <?php if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin())): ?>
                                        <div class="relative" x-data="{ open: false }">
                                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                                </svg>
                                            </button>
                                            <div x-show="open" @click.away="open = false" x-transition
                                                 class="absolute right-0 top-6 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                                <button onclick="editShareComment(<?php echo e($comment->id); ?>)" 
                                                        class="w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100">Edit</button>
                                                <button onclick="deleteShareComment(<?php echo e($comment->id); ?>)" 
                                                        class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50">Delete</button>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="comment-content" id="comment-content-<?php echo e($comment->id); ?>">
                                <p class="text-gray-700 text-sm"><?php echo nl2br(e($comment->content)); ?></p>
                            </div>
                            
                            <!-- Edit Form (hidden by default) -->
                            <div class="edit-comment-form hidden mt-2" id="edit-comment-form-<?php echo e($comment->id); ?>">
                                <form class="share-comment-edit-form" data-comment-id="<?php echo e($comment->id); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PUT'); ?>
                                    <textarea name="content" rows="2" 
                                              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"><?php echo e($comment->content); ?></textarea>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="cancelEditShareComment(<?php echo e($comment->id); ?>)" 
                                                class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                                        <button type="submit" 
                                                class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                            Save
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Comment Actions -->
                        <div class="flex items-center space-x-4 mt-1 ml-3">
                            <?php if(auth()->guard()->check()): ?>
                                <button onclick="toggleShareCommentLike(<?php echo e($comment->id); ?>)" 
                                        class="text-xs text-gray-500 hover:text-red-600 transition-colors"
                                        id="share-comment-like-btn-<?php echo e($comment->id); ?>">
                                    <?php
                                        $isLiked = $comment->isLikedBy(auth()->user());
                                    ?>
                                    <span class="<?php echo e($isLiked ? 'text-red-600 font-medium' : ''); ?>">
                                        <?php echo e($isLiked ? '❤️ Like' : 'Like'); ?>

                                    </span>
                                    <span id="share-comment-likes-count-<?php echo e($comment->id); ?>" class="ml-1">
                                        <?php if($comment->likes->count() > 0): ?>
                                            (<?php echo e($comment->likes->count()); ?>)
                                        <?php endif; ?>
                                    </span>
                                </button>
                            <?php endif; ?>
                            
                            <button onclick="toggleShareCommentReply(<?php echo e($comment->id); ?>)" 
                                    class="text-xs text-gray-500 hover:text-blue-600 transition-colors">
                                Reply
                            </button>
                        </div>

                        <!-- Reply Form (hidden by default) -->
                        <div class="reply-form hidden mt-2 ml-3" id="share-reply-form-<?php echo e($comment->id); ?>">
                            <form class="share-comment-reply-form" data-parent-id="<?php echo e($comment->id); ?>" data-share-id="<?php echo e($share->id); ?>">
                                <?php echo csrf_field(); ?>
                                <div class="flex space-x-2">
                                    <img class="h-6 w-6 rounded-full" 
                                         src="<?php echo e(auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name ?? 'User') . '&color=7BC74D&background=EEEEEE'); ?>" 
                                         alt="<?php echo e(auth()->user()->name ?? 'User'); ?>">
                                    <div class="flex-1">
                                        <textarea name="content" rows="1" 
                                                  placeholder="Write a reply..." 
                                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                                        <div class="mt-1 flex justify-end space-x-2">
                                            <button type="button" onclick="cancelShareCommentReply(<?php echo e($comment->id); ?>)" 
                                                    class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">Cancel</button>
                                            <button type="submit" 
                                                    class="px-2 py-1 bg-custom-green text-white text-xs font-medium rounded hover:bg-custom-second-darkest">
                                                Reply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        <?php if($comment->replies->count() > 0): ?>
                            <div class="mt-3 ml-6 space-y-2">
                                <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="reply-item flex space-x-2" data-comment-id="<?php echo e($reply->id); ?>">
                                        <img class="h-6 w-6 rounded-full" 
                                             src="<?php echo e($reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                                             alt="<?php echo e($reply->user->name); ?>">
                                        <div class="flex-1">
                                            <div class="bg-white rounded-lg px-3 py-2 shadow-sm">
                                                <div class="flex items-center justify-between mb-1">
                                                    <span class="font-medium text-gray-900 text-sm"><?php echo e($reply->user->name); ?></span>
                                                    <span class="text-xs text-gray-500"><?php echo e($reply->created_at->diffForHumans()); ?></span>
                                                </div>
                                                <p class="text-gray-700 text-sm"><?php echo nl2br(e($reply->content)); ?></p>
                                            </div>
                                            
                                            <!-- Reply Actions -->
                                            <div class="flex items-center space-x-4 mt-1 ml-3">
                                                <?php if(auth()->guard()->check()): ?>
                                                    <button onclick="toggleShareCommentLike(<?php echo e($reply->id); ?>)" 
                                                            class="text-xs text-gray-500 hover:text-red-600 transition-colors"
                                                            id="share-comment-like-btn-<?php echo e($reply->id); ?>">
                                                        <?php
                                                            $isReplyLiked = $reply->isLikedBy(auth()->user());
                                                        ?>
                                                        <span class="<?php echo e($isReplyLiked ? 'text-red-600 font-medium' : ''); ?>">
                                                            <?php echo e($isReplyLiked ? '❤️ Like' : 'Like'); ?>

                                                        </span>
                                                        <span id="share-comment-likes-count-<?php echo e($reply->id); ?>" class="ml-1">
                                                            <?php if($reply->likes->count() > 0): ?>
                                                                (<?php echo e($reply->likes->count()); ?>)
                                                            <?php endif; ?>
                                                        </span>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p class="text-gray-500 text-sm text-center py-4">No comments yet. Be the first to comment!</p>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/share-comment-section.blade.php ENDPATH**/ ?>