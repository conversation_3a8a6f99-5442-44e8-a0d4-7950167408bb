<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Organization;
use App\Models\Group;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get regular posts
        $posts = Post::with(['user', 'organization', 'comments', 'likes', 'shares'])
            ->published()
            ->latest('published_at')
            ->get();

        // Get shared posts (timeline shares only) - filtered by privacy scope
        $shares = \App\Models\Share::with(['user', 'post.user', 'post.organization', 'post.comments', 'post.likes', 'post.shares'])
            ->where('share_type', 'direct')
            ->visibleTo(auth()->user())
            ->whereHas('post', function($query) {
                $query->published();
            })
            ->latest('created_at')
            ->get();

        // Combine and sort by date
        $feedItems = collect();

        // Add posts with type indicator
        foreach ($posts as $post) {
            $feedItems->push((object)[
                'type' => 'post',
                'data' => $post,
                'created_at' => $post->published_at,
            ]);
        }

        // Add shares with type indicator
        foreach ($shares as $share) {
            $feedItems->push((object)[
                'type' => 'share',
                'data' => $share,
                'created_at' => $share->created_at,
            ]);
        }

        // Sort by date (newest first) and paginate
        $feedItems = $feedItems->sortByDesc('created_at');

        // Manual pagination
        $perPage = 10;
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $feedItems->slice($offset, $perPage);

        $posts = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $feedItems->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return view('posts.index', compact('posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $organizations = collect();
        
        // Get organizations where user is a member
        if (auth()->check()) {
            $organizations = auth()->user()->activeOrganizations()->get();
        }

        return view('posts.create', compact('organizations'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => ['required', Rule::in(['announcement', 'event', 'financial_report', 'general'])],
            'organization_id' => 'nullable|exists:organizations,id',
            'group_id' => 'nullable|exists:groups,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:51200', // 50MB max
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'nullable',
            'status' => 'nullable|string',
        ]);

        // Convert empty IDs to null
        if (empty($validated['organization_id'])) {
            $validated['organization_id'] = null;
        }
        if (empty($validated['group_id'])) {
            $validated['group_id'] = null;
        }

        // Ensure only one of organization_id or group_id is set
        if ($validated['organization_id'] && $validated['group_id']) {
            return back()->withErrors(['error' => 'Post cannot belong to both organization and group.']);
        }

        // Check if user can post to the selected organization
        if ($validated['organization_id']) {
            $organization = Organization::findOrFail($validated['organization_id']);
            $membership = $organization->members()->where('user_id', auth()->id())->first();

            if (!$membership || $membership->pivot->status !== 'active') {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization.']);
            }
        }

        // Check if user can post to the selected group
        if ($validated['group_id']) {
            $group = Group::findOrFail($validated['group_id']);

            if (!$group->hasActiveMember(auth()->user())) {
                return back()->withErrors(['group_id' => 'You must be an active member to post in this group.']);
            }
        }

        $validated['user_id'] = auth()->id();

        // Set default status if not provided
        if (!isset($validated['status'])) {
            $validated['status'] = 'published';
        }

        // Handle post approval for groups
        if ($validated['group_id']) {
            $group = Group::find($validated['group_id']);
            if ($group->post_approval === 'required' && !$group->userCanModerate(auth()->user())) {
                $validated['approval_status'] = 'pending';
                $validated['status'] = 'published'; // Published but pending approval
                $validated['published_at'] = now();
            } else {
                $validated['approval_status'] = 'approved';
                $validated['approved_at'] = now();
                $validated['approved_by'] = auth()->id();
                $validated['published_at'] = $validated['status'] === 'published' ? now() : null;
            }
        } else {
            // For organizations and personal posts, auto-approve
            $validated['approval_status'] = 'approved';
            $validated['approved_at'] = now();
            $validated['approved_by'] = auth()->id();
            $validated['published_at'] = $validated['status'] === 'published' ? now() : null;
        }

        // Convert is_pinned to boolean if it exists
        if (isset($validated['is_pinned'])) {
            $validated['is_pinned'] = filter_var($validated['is_pinned'], FILTER_VALIDATE_BOOLEAN);
        } else {
            $validated['is_pinned'] = false;
        }

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $imagePaths[] = $path;
            }
            $validated['images'] = $imagePaths;
        }

        $post = Post::create($validated);

        // Handle file attachments for groups
        if ($request->hasFile('attachments') && $validated['group_id']) {
            $group = Group::find($validated['group_id']);
            if ($group && $group->allow_file_sharing) {
                foreach ($request->file('attachments') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $extension = $file->getClientOriginalExtension();
                    $size = $file->getSize();

                    // Check file size limit
                    if ($size > ($group->max_file_size_mb * 1024 * 1024)) {
                        continue; // Skip files that are too large
                    }

                    // Check allowed file types
                    if ($group->allowed_file_types && !in_array(strtolower($extension), $group->allowed_file_types)) {
                        continue; // Skip disallowed file types
                    }

                    $path = $file->store('groups/attachments', 'public');
                    $filename = basename($path); // Extract the generated filename from the path

                    $post->fileAttachments()->create([
                        'filename' => $filename,
                        'file_path' => $path,
                        'original_filename' => $originalName,
                        'file_type' => strtolower($extension),
                        'file_size' => $size,
                        'mime_type' => $file->getMimeType(),
                    ]);
                }
            }
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post created successfully!',
                'post' => $post->load(['user', 'organization', 'group'])
            ]);
        }

        // Redirect based on post type
        if ($post->group_id) {
            $message = $post->approval_status === 'pending'
                ? 'Post submitted for approval!'
                : 'Post created successfully!';
            return redirect()->route('groups.show', $post->group)
                ->with('success', $message);
        } elseif ($post->organization_id) {
            return redirect()->route('organizations.page', $post->organization)
                ->with('success', 'Post created successfully!');
        } else {
            return redirect()->route('posts.show', $post)
                ->with('success', 'Post created successfully!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Post $post)
    {
        $post->load([
            'user',
            'organization',
            'comments' => function ($query) {
                $query->with('user', 'replies.user')->whereNull('parent_id');
            },
            'likes.user',
            'shares.user'
        ]);

        return view('posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $organizations = auth()->user()->activeOrganizations()->get();

        return view('posts.edit', compact('post', 'organizations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => ['required', Rule::in(['announcement', 'event', 'financial_report', 'general'])],
            'organization_id' => 'nullable|exists:organizations,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'boolean',
            'status' => ['required', Rule::in(['published', 'draft'])],
            'remove_images' => 'nullable|array',
            'remove_images.*' => 'string',
        ]);

        // Check organization membership if changing organization
        if ($validated['organization_id'] && $validated['organization_id'] !== $post->organization_id) {
            $organization = Organization::findOrFail($validated['organization_id']);
            $membership = $organization->members()->where('user_id', auth()->id())->first();
            
            if (!$membership || $membership->pivot->status !== 'active') {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization.']);
            }
        }

        // Handle published_at timestamp
        if ($validated['status'] === 'published' && !$post->published_at) {
            $validated['published_at'] = now();
        } elseif ($validated['status'] === 'draft') {
            $validated['published_at'] = null;
        }

        // Handle image removal
        $currentImages = $post->images ?? [];
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (in_array($imageToRemove, $currentImages)) {
                    Storage::disk('public')->delete($imageToRemove);
                    $currentImages = array_filter($currentImages, fn($img) => $img !== $imageToRemove);
                }
            }
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $currentImages[] = $path;
            }
        }

        $validated['images'] = array_values($currentImages);

        $post->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post updated successfully!',
                'post' => $post->load(['user', 'organization'])
            ]);
        }

        return redirect()->route('posts.show', $post)
            ->with('success', 'Post updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post)
    {
        // Check if user can delete this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        // Delete associated images
        if ($post->images) {
            foreach ($post->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $post->delete();

        return redirect()->route('dashboard')
            ->with('success', 'Post deleted successfully!');
    }

    /**
     * Toggle like on a post
     */
    public function toggleLike(Post $post)
    {
        $user = auth()->user();
        $like = $post->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $liked = false;
        } else {
            $post->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $post->likes()->count()
        ]);
    }

    /**
     * Filter posts based on criteria
     */
    public function filter(Request $request)
    {
        // Get regular posts
        $postsQuery = Post::with(['user', 'organization', 'comments', 'likes', 'shares', 'fileAttachments'])
            ->published();

        // Get shared posts (timeline shares only) - filtered by privacy scope
        $sharesQuery = \App\Models\Share::with(['user', 'post.user', 'post.organization', 'post.comments', 'post.likes', 'post.shares', 'post.fileAttachments'])
            ->where('share_type', 'direct')
            ->visibleTo(auth()->user())
            ->whereHas('post', function($query) {
                $query->published();
            });

        // Apply filters to both queries
        if ($request->has('type') && $request->type !== 'all') {
            $postsQuery->where('type', $request->type);
            $sharesQuery->whereHas('post', function($query) use ($request) {
                $query->where('type', $request->type);
            });
        }

        if ($request->has('organization_filter')) {
            if ($request->organization_filter === 'personal') {
                $postsQuery->whereNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNull('organization_id');
                });
            } elseif ($request->organization_filter === 'organizations') {
                $postsQuery->whereNotNull('organization_id');
                $sharesQuery->whereHas('post', function($query) {
                    $query->whereNotNull('organization_id');
                });
            }
        }

        if ($request->has('with_images') && $request->with_images === 'true') {
            $postsQuery->whereNotNull('images')
                      ->where(function($q) {
                          $q->where('images', '!=', '[]')
                            ->where('images', '!=', 'null');
                      });
            $sharesQuery->whereHas('post', function($query) {
                $query->whereNotNull('images')
                      ->where(function($q) {
                          $q->where('images', '!=', '[]')
                            ->where('images', '!=', 'null');
                      });
            });
        }

        if ($request->has('pinned') && $request->pinned === 'true') {
            $postsQuery->where('is_pinned', true);
            $sharesQuery->whereHas('post', function($query) {
                $query->where('is_pinned', true);
            });
        }

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $postsQuery->where(function($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
            $sharesQuery->where(function($q) use ($searchTerm) {
                $q->where('message', 'like', "%{$searchTerm}%")
                  ->orWhereHas('post', function($query) use ($searchTerm) {
                      $query->where('title', 'like', "%{$searchTerm}%")
                            ->orWhere('content', 'like', "%{$searchTerm}%");
                  });
            });
        }

        // Get posts and shares
        $posts = $postsQuery->latest('published_at')->get();
        $shares = $sharesQuery->latest('created_at')->get();

        // Combine and sort by date
        $feedItems = collect();

        // Add posts with type indicator
        foreach ($posts as $post) {
            $feedItems->push((object)[
                'type' => 'post',
                'data' => $post,
                'created_at' => $post->published_at,
            ]);
        }

        // Add shares with type indicator
        foreach ($shares as $share) {
            $feedItems->push((object)[
                'type' => 'share',
                'data' => $share,
                'created_at' => $share->created_at,
            ]);
        }

        // Sort by date (newest first) and paginate
        $feedItems = $feedItems->sortByDesc('created_at');

        // Manual pagination
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedItems = $feedItems->slice($offset, $perPage);

        $paginatedFeedItems = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedItems,
            $feedItems->count(),
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );

        // Return filtered posts as HTML for AJAX
        $html = '';
        foreach ($paginatedFeedItems as $feedItem) {
            if ($feedItem->type === 'post') {
                $html .= view('components.post-card', ['post' => $feedItem->data])->render();
            } elseif ($feedItem->type === 'share') {
                $html .= view('components.shared-post-card', ['share' => $feedItem->data])->render();
            }
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => [
                'current_page' => $paginatedFeedItems->currentPage(),
                'last_page' => $paginatedFeedItems->lastPage(),
                'total' => $paginatedFeedItems->total(),
                'has_more' => $paginatedFeedItems->hasMorePages(),
                'links' => $paginatedFeedItems->links()->render()
            ],
            'count' => $paginatedFeedItems->count(),
            'total' => $paginatedFeedItems->total()
        ]);
    }
}
