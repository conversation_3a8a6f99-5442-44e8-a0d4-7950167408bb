@props(['share', 'showInline' => false])

<div class="share-comment-section p-4 bg-gray-50">
    <!-- Comment Form -->
    @auth
        <form class="share-comment-form mb-4" data-share-id="{{ $share->id }}">
            @csrf
            <div class="flex space-x-3">
                <img class="h-8 w-8 rounded-full" 
                     src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" 
                     alt="{{ auth()->user()->name }}">
                <div class="flex-1">
                    <textarea name="content" rows="2" 
                              placeholder="Write a comment..." 
                              class="w-full border-gray-300 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                    <div class="mt-2 flex justify-end">
                        <button type="submit" 
                                class="px-4 py-1 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                            Comment
                        </button>
                    </div>
                </div>
            </div>
        </form>
    @endauth

    <!-- Comments List -->
    <div class="space-y-3" id="share-comments-list-{{ $share->id }}">
        @forelse($share->comments()->whereNull('parent_id')->with(['user', 'replies.user', 'likes'])->latest()->get() as $comment)
            <div class="comment-item" data-comment-id="{{ $comment->id }}">
                <div class="flex space-x-3">
                    <img class="h-8 w-8 rounded-full" 
                         src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                         alt="{{ $comment->user->name }}">
                    <div class="flex-1">
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="flex items-center space-x-2 mb-1">
                                <a href="{{ route('profile.user', $comment->user) }}" class="font-medium text-gray-900 hover:text-custom-green text-sm">
                                    {{ $comment->user->name }}
                                </a>
                                <span class="text-xs text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                                @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                                    <div class="relative ml-auto" x-data="{ open: false }">
                                        <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                            </svg>
                                        </button>
                                        <div x-show="open" @click.away="open = false" x-transition
                                             class="absolute right-0 top-8 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                            <button onclick="editShareComment({{ $comment->id }})"
                                                    class="w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                <span>Edit</span>
                                            </button>
                                            <button onclick="deleteShareComment({{ $comment->id }})"
                                                    class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                <span>Delete</span>
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="comment-content" id="comment-content-{{ $comment->id }}">
                                <p class="text-gray-700 text-sm">{!! nl2br(e($comment->content)) !!}</p>
                            </div>
                            
                            <!-- Edit Form (hidden by default) -->
                            <div class="edit-comment-form hidden mt-2" id="edit-comment-form-{{ $comment->id }}">
                                <form class="share-comment-edit-form" data-comment-id="{{ $comment->id }}">
                                    @csrf
                                    @method('PUT')
                                    <textarea name="content" rows="2" 
                                              class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm">{{ $comment->content }}</textarea>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="cancelEditShareComment({{ $comment->id }})" 
                                                class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                                        <button type="submit" 
                                                class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                            Save
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Comment Actions -->
                        <div class="comment-actions flex items-center space-x-4 mt-2 text-sm">
                            @auth
                                <button onclick="toggleShareCommentLike({{ $comment->id }})"
                                        class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                                        id="share-comment-like-btn-{{ $comment->id }}">
                                    @php
                                        $isLiked = $comment->isLikedBy(auth()->user());
                                    @endphp
                                    <svg class="w-4 h-4 {{ $isLiked ? 'text-red-600 fill-current' : '' }}"
                                         fill="{{ $isLiked ? 'currentColor' : 'none' }}"
                                         stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    <span id="share-comment-like-count-{{ $comment->id }}">{{ $comment->likes->count() }}</span>
                                </button>
                            @else
                                <span class="flex items-center space-x-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    <span>{{ $comment->likes->count() }}</span>
                                </span>
                            @endauth

                            @auth
                                <button onclick="toggleShareCommentReply({{ $comment->id }})" class="text-gray-500 hover:text-blue-600 transition-colors">
                                    Reply
                                </button>
                            @endauth

                            @if($comment->created_at != $comment->updated_at)
                                <span class="text-xs text-gray-400">• edited</span>
                            @endif
                        </div>

                        <!-- Reply Form (hidden by default) -->
                        <div class="reply-form hidden mt-2 ml-3" id="share-reply-form-{{ $comment->id }}">
                            <form class="share-comment-reply-form" data-parent-id="{{ $comment->id }}" data-share-id="{{ $share->id }}">
                                @csrf
                                <div class="flex space-x-2">
                                    <img class="h-6 w-6 rounded-full" 
                                         src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name ?? 'User') . '&color=7BC74D&background=EEEEEE' }}" 
                                         alt="{{ auth()->user()->name ?? 'User' }}">
                                    <div class="flex-1">
                                        <textarea name="content" rows="1" 
                                                  placeholder="Write a reply..." 
                                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                                        <div class="mt-1 flex justify-end space-x-2">
                                            <button type="button" onclick="cancelShareCommentReply({{ $comment->id }})" 
                                                    class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">Cancel</button>
                                            <button type="submit" 
                                                    class="px-2 py-1 bg-custom-green text-white text-xs font-medium rounded hover:bg-custom-second-darkest">
                                                Reply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        @if($comment->replies->count() > 0)
                            <div class="nested-comments mt-4 space-y-3">
                                @foreach($comment->replies as $reply)
                                    <div class="comment-item" data-comment-id="{{ $reply->id }}">
                                        <div class="flex space-x-3">
                                            <a href="{{ route('profile.user', $reply->user) }}">
                                                <img class="h-8 w-8 rounded-full flex-shrink-0"
                                                     src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                     alt="{{ $reply->user->name }}">
                                            </a>
                                            <div class="flex-1">
                                                <div class="bg-gray-50 rounded-lg p-3">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <a href="{{ route('profile.user', $reply->user) }}" class="font-medium text-gray-900 hover:text-custom-green text-sm">
                                                            {{ $reply->user->name }}
                                                        </a>
                                                        <span class="text-xs text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                                        @if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin()))
                                                            <div class="relative ml-auto" x-data="{ open: false }">
                                                                <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1">
                                                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                                                    </svg>
                                                                </button>
                                                                <div x-show="open" @click.away="open = false" x-transition
                                                                     class="absolute right-0 top-8 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                                                    <button onclick="editShareComment({{ $reply->id }})"
                                                                            class="w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                                        </svg>
                                                                        <span>Edit</span>
                                                                    </button>
                                                                    <button onclick="deleteShareComment({{ $reply->id }})"
                                                                            class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                        </svg>
                                                                        <span>Delete</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>

                                                    <div class="comment-content" id="comment-content-{{ $reply->id }}">
                                                        <p class="text-gray-700 text-sm">{!! nl2br(e($reply->content)) !!}</p>
                                                    </div>

                                                    <!-- Edit Form for Reply (hidden by default) -->
                                                    <div class="edit-comment-form hidden mt-2" id="edit-comment-form-{{ $reply->id }}">
                                                        <form class="share-comment-edit-form" data-comment-id="{{ $reply->id }}">
                                                            @csrf
                                                            @method('PUT')
                                                            <textarea name="content" rows="2"
                                                                      class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm">{{ $reply->content }}</textarea>
                                                            <div class="mt-2 flex justify-end space-x-2">
                                                                <button type="button" onclick="cancelEditShareComment({{ $reply->id }})"
                                                                        class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                                                                <button type="submit"
                                                                        class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                                                    Save
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>

                                                <!-- Reply Actions -->
                                                <div class="comment-actions flex items-center space-x-4 mt-2 text-sm">
                                                    @auth
                                                        <button onclick="toggleShareCommentLike({{ $reply->id }})"
                                                                class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                                                                id="share-comment-like-btn-{{ $reply->id }}">
                                                            @php
                                                                $isReplyLiked = $reply->isLikedBy(auth()->user());
                                                            @endphp
                                                            <svg class="w-4 h-4 {{ $isReplyLiked ? 'text-red-600 fill-current' : '' }}"
                                                                 fill="{{ $isReplyLiked ? 'currentColor' : 'none' }}"
                                                                 stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                            </svg>
                                                            <span id="share-comment-like-count-{{ $reply->id }}">{{ $reply->likes->count() }}</span>
                                                        </button>
                                                    @else
                                                        <span class="flex items-center space-x-1 text-gray-400">
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                            </svg>
                                                            <span>{{ $reply->likes->count() }}</span>
                                                        </span>
                                                    @endauth

                                                    @if($reply->created_at != $reply->updated_at)
                                                        <span class="text-xs text-gray-400">• edited</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="no-comments text-gray-500 text-center py-8">
                <svg class="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p>No comments yet. Be the first to comment!</p>
            </div>
        @endforelse
    </div>
</div>

<style>
.share-comment-section .share-comment-form textarea:focus {
    min-height: 80px;
    transition: min-height 0.2s ease;
}

.share-comment-section .reply-form {
    margin-top: 12px;
    padding-left: 40px;
}

.share-comment-section .comment-item {
    transition: background-color 0.2s ease;
}

.share-comment-section .comment-item:hover {
    background-color: rgba(0, 0, 0, 0.01);
}

.share-comment-section .comment-actions button {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.share-comment-section .comment-actions button:hover {
    transform: translateY(-1px);
}

.share-comment-section .nested-comments {
    border-left: 2px solid #e5e7eb;
    margin-left: 20px;
    padding-left: 20px;
}

.share-comment-section .comment-edit-form textarea {
    font-size: 0.875rem;
}

.share-comment-section .reply-form textarea {
    font-size: 0.875rem;
}
</style>
