@props(['comment', 'post'])

<div class="comment-item" data-comment-id="{{ $comment->id }}">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}">
            <img class="h-10 w-10 rounded-full flex-shrink-0" 
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                 alt="{{ $comment->user->name }}">
        </a>
        <div class="flex-1">
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-1">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-medium text-gray-900 hover:text-custom-green text-sm">
                        {{ $comment->user->name }}
                    </a>
                    <span class="text-xs text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                    @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
                                <button onclick="editComment({{ $comment->id }})"
                                        class="w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                    <span>Edit</span>
                                </button>
                                <button onclick="deleteComment({{ $comment->id }})"
                                        class="w-full text-left px-3 py-1 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="comment-content" id="comment-content-{{ $comment->id }}">
                    <p class="text-gray-700 text-sm">{!! nl2br(e($comment->content)) !!}</p>
                </div>

                <!-- Edit Form (hidden by default) -->
                <div class="edit-comment-form hidden mt-2" id="edit-comment-form-{{ $comment->id }}">
                    <form class="edit-comment-form" data-comment-id="{{ $comment->id }}">
                        @csrf
                        @method('PUT')
                        <textarea name="content" rows="2"
                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm">{{ $comment->content }}</textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment({{ $comment->id }})"
                                    class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                            <button type="submit"
                                    class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 text-sm">
                @auth
                    <button onclick="toggleCommentLike({{ $comment->id }})"
                            class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                            id="comment-like-btn-{{ $comment->id }}">
                        @php
                            $isLiked = $comment->isLikedBy(auth()->user());
                        @endphp
                        <svg class="w-4 h-4 {{ $isLiked ? 'text-red-600 fill-current' : '' }}"
                             fill="{{ $isLiked ? 'currentColor' : 'none' }}"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span id="comment-like-count-{{ $comment->id }}">{{ $comment->likes->count() }}</span>
                    </button>
                @else
                    <span class="flex items-center space-x-1 text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span>{{ $comment->likes->count() }}</span>
                    </span>
                @endauth
                
                @auth
                    <button onclick="showReplyForm({{ $comment->id }})" class="text-gray-500 hover:text-blue-600 transition-colors">
                        Reply
                    </button>
                @endauth

                @if($comment->created_at != $comment->updated_at)
                    <span class="text-xs text-gray-400">• edited</span>
                @endif
            </div>
            
            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-2 ml-3" id="reply-form-{{ $comment->id }}">
                @auth
                    <form class="comment-form" data-post-id="{{ $post->id }}" data-parent-id="{{ $comment->id }}">
                        @csrf
                        <div class="flex space-x-2">
                            <img class="h-6 w-6 rounded-full"
                                 src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                 alt="{{ auth()->user()->name }}">
                            <div class="flex-1">
                                <textarea name="content" rows="1"
                                          placeholder="Write a reply..."
                                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                                <div class="mt-1 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm({{ $comment->id }})"
                                            class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button type="submit"
                                            class="px-2 py-1 bg-custom-green text-white text-xs font-medium rounded hover:bg-custom-second-darkest">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                @endauth
            </div>
            
            <!-- Replies -->
            @if($comment->replies->count() > 0)
                <div class="nested-comments mt-4 space-y-3 border-l-2 border-gray-200 ml-5 pl-5">
                    @foreach($comment->replies as $reply)
                        <x-comment-item :comment="$reply" :post="$post" />
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
