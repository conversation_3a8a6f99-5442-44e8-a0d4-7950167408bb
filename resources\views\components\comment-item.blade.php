@props(['comment', 'post'])

<div class="comment-item" data-comment-id="{{ $comment->id }}">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}">
            <img class="h-10 w-10 rounded-full flex-shrink-0" 
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                 alt="{{ $comment->user->name }}">
        </a>
        <div class="flex-1">
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-1">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                        {{ $comment->user->name }}
                    </a>
                    <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                </div>
                <div class="comment-content">
                    <p class="text-gray-700">{!! nl2br(e($comment->content)) !!}</p>
                </div>
                
                <!-- Edit form (hidden by default) -->
                <div class="comment-edit-form hidden mt-2">
                    <form class="edit-comment-form" data-comment-id="{{ $comment->id }}">
                        @csrf
                        @method('PUT')
                        <textarea name="content" rows="2" 
                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none">{{ $comment->content }}</textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button type="button" onclick="cancelEditComment({{ $comment->id }})" 
                                    class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                            <button type="submit" 
                                    class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                Save
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Comment Actions -->
            <div class="comment-actions flex items-center space-x-4 mt-2 text-sm">
                @auth
                    <button onclick="toggleCommentLike({{ $comment->id }})"
                            class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
                            id="comment-like-btn-{{ $comment->id }}">
                        <svg class="w-4 h-4 {{ $comment->isLikedBy(auth()->user()) ? 'text-red-600 fill-current' : '' }}"
                             fill="{{ $comment->isLikedBy(auth()->user()) ? 'currentColor' : 'none' }}"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span id="comment-like-count-{{ $comment->id }}">{{ $comment->likes->count() }}</span>
                    </button>
                @else
                    <span class="flex items-center space-x-1 text-gray-400">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span>{{ $comment->likes->count() }}</span>
                    </span>
                @endauth
                
                @auth
                    <button onclick="showReplyForm({{ $comment->id }})" class="text-gray-500 hover:text-blue-600 transition-colors">
                        Reply
                    </button>
                @endauth

                @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                    <button onclick="editComment({{ $comment->id }})" class="text-gray-500 hover:text-blue-600 transition-colors">
                        Edit
                    </button>
                    <button onclick="deleteComment({{ $comment->id }})" class="text-gray-500 hover:text-red-600 transition-colors">
                        Delete
                    </button>
                @endif
            </div>
            
            <!-- Reply Form (hidden by default) -->
            <div class="reply-form hidden mt-3" id="reply-form-{{ $comment->id }}">
                @auth
                    <form class="comment-form" data-post-id="{{ $post->id }}" data-parent-id="{{ $comment->id }}">
                        @csrf
                        <div class="flex space-x-3">
                            <img class="h-8 w-8 rounded-full flex-shrink-0" 
                                 src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" 
                                 alt="{{ auth()->user()->name }}">
                            <div class="flex-1">
                                <textarea name="content" rows="2" 
                                          placeholder="Write a reply..." 
                                          class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" 
                                          required></textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="hideReplyForm({{ $comment->id }})" 
                                            class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800">Cancel</button>
                                    <button type="submit" 
                                            class="px-3 py-1 bg-custom-green text-white text-sm font-medium rounded hover:bg-custom-second-darkest">
                                        Reply
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                @endauth
            </div>
            
            <!-- Replies -->
            @if($comment->replies->count() > 0)
                <div class="nested-comments mt-4 space-y-3">
                    @foreach($comment->replies as $reply)
                        <x-comment-item :comment="$reply" :post="$post" />
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
