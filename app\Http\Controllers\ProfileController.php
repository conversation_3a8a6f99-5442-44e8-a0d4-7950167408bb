<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class ProfileController extends Controller
{
    /**
     * Display the user's profile page.
     */
    public function show(Request $request, User $user = null): View
    {
        // If no user specified, show current user's profile
        $user = $user ?? $request->user();

        // Load user relationships
        $user->load([
            'posts' => function ($query) {
                $query->published()
                      ->with(['organization', 'likes', 'comments', 'fileAttachments'])
                      ->latest('published_at');
            },
            'activeOrganizations' => function ($query) {
                $query->withPivot(['role', 'joined_at']);
            },
            'createdOrganizations'
        ]);

        // Get user statistics
        $stats = [
            'posts_count' => $user->posts()->published()->count(),
            'organizations_count' => $user->activeOrganizations()->count(),
            'created_organizations_count' => $user->createdOrganizations()->count(),
            'total_likes' => $user->posts()->published()->withCount('likes')->get()->sum('likes_count'),
            'total_comments' => $user->posts()->published()->withCount('comments')->get()->sum('comments_count'),
        ];

        return view('profile.show', compact('user', 'stats'));
    }

    /**
     * Display the user's profile form (standalone edit page).
     */
    public function edit(Request $request): View
    {
        return view('profile.edit', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $validated = $request->validated();

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($request->user()->avatar) {
                Storage::delete($request->user()->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        $request->user()->fill($validated);

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('profile.show')->with('status', 'profile-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}
