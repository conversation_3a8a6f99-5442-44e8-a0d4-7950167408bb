@props(['share'])

<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <!-- Share Header -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <a href="{{ route('profile.user', $share->user) }}">
                <img class="h-10 w-10 rounded-full" 
                     src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                     alt="{{ $share->user->name }}">
            </a>
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <a href="{{ route('profile.user', $share->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                        {{ $share->user->name }}
                    </a>
                    <span class="text-gray-500">shared a post</span>
                </div>
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <time datetime="{{ $share->created_at->toISOString() }}">
                        {{ $share->created_at->diffForHumans() }}
                    </time>
                    <span>•</span>
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 3.314-2.686 6-6 6s-6-2.686-6-6a4.75 4.75 0 01.332-1.973z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- Share Message (if any) -->
        @if($share->message)
            <div class="mt-3 text-gray-700">
                <p>{!! nl2br(e($share->message)) !!}</p>
            </div>
        @endif
    </div>

    <!-- Original Post Content (Embedded) -->
    <div class="mx-4 mb-4 border border-gray-200 rounded-lg overflow-hidden">
        <!-- Original Post Header -->
        <div class="p-4 bg-gray-50">
            <div class="flex items-center space-x-3">
                @if($share->post->organization)
                    <a href="{{ route('organizations.show', $share->post->organization) }}">
                        <img class="h-8 w-8 rounded-full" 
                             src="{{ $share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=3B82F6&background=DBEAFE' }}" 
                             alt="{{ $share->post->organization->name }}">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('organizations.show', $share->post->organization) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                {{ $share->post->organization->name }}
                            </a>
                            <span class="text-gray-500 text-sm">•</span>
                            <a href="{{ route('profile.user', $share->post->user) }}" class="text-sm text-gray-600 hover:text-custom-green">
                                by {{ $share->post->user->name }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $share->post->published_at->diffForHumans() }}
                        </div>
                    </div>
                @else
                    <a href="{{ route('profile.user', $share->post->user) }}">
                        <img class="h-8 w-8 rounded-full" 
                             src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                             alt="{{ $share->post->user->name }}">
                    </a>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <a href="{{ route('profile.user', $share->post->user) }}" class="font-medium text-gray-900 hover:text-custom-green">
                                {{ $share->post->user->name }}
                            </a>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ $share->post->published_at->diffForHumans() }}
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Original Post Content -->
        <div class="p-4 bg-white">
            <a href="{{ route('posts.show', $share->post) }}" class="block hover:bg-gray-50 transition-colors rounded">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $share->post->title }}</h3>
                
                @if($share->post->content)
                    <div class="text-gray-700 mb-3">
                        <p>{{ Str::limit($share->post->content, 200) }}</p>
                    </div>
                @endif

                <!-- Post Images -->
                @if($share->post->images && count($share->post->images) > 0)
                    <div class="mb-3">
                        @if(count($share->post->images) == 1)
                            <div class="rounded-lg overflow-hidden">
                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->images[0]) }}" 
                                     alt="Post image" 
                                     class="w-full h-64 object-cover">
                            </div>
                        @else
                            <div class="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                                @foreach(array_slice($share->post->images, 0, 4) as $index => $image)
                                    <div class="relative {{ $index >= 2 ? 'hidden sm:block' : '' }}">
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}" 
                                             alt="Post image {{ $index + 1 }}" 
                                             class="w-full h-32 object-cover">
                                        @if($index == 3 && count($share->post->images) > 4)
                                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                <span class="text-white font-semibold">+{{ count($share->post->images) - 4 }}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Post Type Badge -->
                @if($share->post->type !== 'general')
                    <div class="mb-3">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($share->post->type === 'announcement') bg-blue-100 text-blue-800
                            @elseif($share->post->type === 'event') bg-green-100 text-green-800
                            @elseif($share->post->type === 'job') bg-purple-100 text-purple-800
                            @elseif($share->post->type === 'scholarship') bg-yellow-100 text-yellow-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($share->post->type) }}
                        </span>
                    </div>
                @endif
            </a>
        </div>
    </div>

    <!-- Interaction Stats -->
    <div class="px-4 py-2 border-t border-gray-100">
        <div class="flex items-center justify-between text-sm text-gray-500">
            <div class="flex items-center space-x-4">
                @if($share->post->likes->count() > 0)
                    <span class="flex items-center space-x-1">
                        <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                        </svg>
                        <span>{{ $share->post->likes->count() }}</span>
                    </span>
                @endif
                
                @if($share->post->comments->count() > 0)
                    <span>{{ $share->post->comments->count() }} comments</span>
                @endif
                
                @if($share->post->shares->count() > 0)
                    <span>{{ $share->post->shares->count() }} shares</span>
                @endif
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="px-4 py-3 border-t border-gray-100">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <!-- Like Button -->
                @auth
                    <button onclick="toggleLike({{ $share->post->id }})" 
                            class="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors"
                            id="like-btn-{{ $share->post->id }}">
                        @php
                            $isLiked = $share->post->isLikedBy(auth()->user());
                        @endphp
                        <svg class="w-5 h-5 {{ $isLiked ? 'text-red-600 fill-current' : '' }}" 
                             fill="{{ $isLiked ? 'currentColor' : 'none' }}" 
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm" id="like-count-{{ $share->post->id }}">{{ $share->post->likes->count() }} likes</span>
                    </button>
                @else
                    <span class="flex items-center space-x-2 text-gray-400">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span class="text-sm">{{ $share->post->likes->count() }} likes</span>
                    </span>
                @endauth

                <!-- Comment Button -->
                <button onclick="toggleComments({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="text-sm" id="comments-count-{{ $share->post->id }}">{{ $share->post->comments->count() }} comments</span>
                </button>

                <!-- Share Button -->
                <button onclick="openShareModal({{ $share->post->id }})" class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="text-sm" id="shares-count-{{ $share->post->id }}">{{ $share->post->shares->count() }} shares</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Inline Comments Section (hidden by default) -->
    <div id="comments-section-{{ $share->post->id }}" class="hidden border-t border-gray-200">
        <x-comment-section :post="$share->post" :showInline="true" />
    </div>

    <!-- Share Modal -->
    <x-share-modal :post="$share->post" />
</div>
